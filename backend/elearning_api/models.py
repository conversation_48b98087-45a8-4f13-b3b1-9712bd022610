from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver
import uuid


def upload_course_thumbnail(instance, filename):
    return f'courses/thumbnails/{instance.id}/{filename}'


def upload_lesson_content(instance, filename):
    return f'courses/{instance.course.id}/lessons/{instance.id}/{filename}'


def upload_assignment_file(instance, filename):
    return f'courses/{instance.course.id}/assignments/{instance.id}/{filename}'


def upload_submission_file(instance, filename):
    return f'submissions/{instance.assignment.id}/{instance.student.id}/{filename}'


class Subject(models.Model):
    """Môn học"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#1976d2')  # Hex color
    icon = models.CharField(max_length=50, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class Grade(models.Model):
    """Khối lớp"""
    name = models.CharField(max_length=50, unique=True)  # Lớp 6, Lớp 7, etc.
    description = models.TextField(blank=True)
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['order']


class Course(models.Model):
    """Khóa học online"""
    DIFFICULTY_CHOICES = [
        ('beginner', 'Cơ bản'),
        ('intermediate', 'Trung bình'),
        ('advanced', 'Nâng cao'),
        ('expert', 'Chuyên sâu'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Bản nháp'),
        ('published', 'Đã xuất bản'),
        ('archived', 'Đã lưu trữ'),
    ]

    # Primary key as UUID for security
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic information
    title = models.CharField(max_length=255)
    description = models.TextField()
    thumbnail = models.ImageField(
        upload_to=upload_course_thumbnail, blank=True, null=True)

    # Academic information
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    grade = models.ForeignKey(Grade, on_delete=models.CASCADE)
    difficulty = models.CharField(
        max_length=20, choices=DIFFICULTY_CHOICES, default='beginner')

    # Teacher information
    teacher = models.ForeignKey(User, on_delete=models.CASCADE, limit_choices_to={
                                'profile__is_teacher': True})

    # Pricing
    price = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)  # VND
    original_price = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)  # VND

    # Course details
    duration = models.CharField(max_length=100)  # "3 tháng", "6 tuần", etc.
    estimated_time = models.IntegerField(default=0)  # Total minutes
    language = models.CharField(max_length=50, default='Tiếng Việt')
    has_certificate = models.BooleanField(default=True)

    # Course content
    objectives = models.JSONField(
        default=list, blank=True)  # Learning objectives
    prerequisites = models.JSONField(default=list, blank=True)  # Prerequisites
    features = models.JSONField(default=list, blank=True)  # Course features
    syllabus = models.JSONField(default=list, blank=True)  # Course syllabus

    # Status and metadata
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='draft')
    is_published = models.BooleanField(default=False)
    max_students = models.IntegerField(default=100)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return self.title

    @property
    def enrolled_count(self):
        return self.enrollments.filter(status='active').count()

    @property
    def discount_percentage(self):
        if self.original_price > self.price > 0:
            return int(((self.original_price - self.price) / self.original_price) * 100)
        return 0

    @property
    def is_free(self):
        return self.price == 0

    @property
    def total_lessons(self):
        return self.lessons.count()

    @property
    def total_quizzes(self):
        return self.quizzes.count()

    @property
    def total_assignments(self):
        return self.assignments.count()

    @property
    def average_rating(self):
        reviews = self.reviews.all()
        if reviews:
            return sum(review.rating for review in reviews) / len(reviews)
        return 0

    @property
    def total_reviews(self):
        return self.reviews.count()

    class Meta:
        ordering = ['-created_at']


class Enrollment(models.Model):
    """Đăng ký khóa học"""
    STATUS_CHOICES = [
        ('active', 'Đang học'),
        ('completed', 'Hoàn thành'),
        ('dropped', 'Đã bỏ học'),
        ('suspended', 'Tạm dừng'),
    ]

    student = models.ForeignKey(User, on_delete=models.CASCADE, limit_choices_to={
                                'profile__is_student': True})
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name='enrollments')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='active')

    # Payment information
    paid_amount = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)
    payment_method = models.CharField(max_length=50, blank=True)
    payment_date = models.DateTimeField(null=True, blank=True)

    # Progress tracking
    progress_percentage = models.FloatField(
        default=0.0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    completed_lessons = models.IntegerField(default=0)
    total_time_spent = models.IntegerField(default=0)  # Minutes

    # Timestamps
    enrolled_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)

    def calculate_progress(self):
        """Calculate actual progress based on completed lessons"""
        total_lessons = self.course.lessons.filter(is_required=True).count()
        if total_lessons == 0:
            return 0.0, 0

        completed_lessons = LessonProgress.objects.filter(
            enrollment=self,
            lesson__is_required=True,
            is_completed=True
        ).count()

        progress_percentage = (completed_lessons / total_lessons) * 100
        return progress_percentage, completed_lessons

    def update_progress(self):
        """Update progress fields based on actual lesson completion"""
        progress_percentage, completed_lessons = self.calculate_progress()

        old_status = self.status
        old_progress = self.progress_percentage
        old_completed = self.completed_lessons
        old_completed_at = self.completed_at

        self.progress_percentage = progress_percentage
        self.completed_lessons = completed_lessons

        # Update status if course is completed
        if progress_percentage >= 100 and self.status == 'active':
            self.status = 'completed'
            self.completed_at = timezone.now()

        self.save()
        # Create certificate if course is completed and has certificate
        if self.status == 'completed' and self.course.has_certificate:
            self._create_certificate_if_not_exists()

        return progress_percentage, completed_lessons

    def _create_certificate_if_not_exists(self):
        """Create certificate if it doesn't exist"""
        try:
            from django.db.models import Max

            # Check if certificate already exists
            if Certificate.objects.filter(student=self.student, course=self.course, enrollment=self).exists():
                return

            # Calculate final score from quiz attempts
            quiz_attempts = QuizAttempt.objects.filter(
                student=self.student,
                quiz__course=self.course,
                status='submitted'
            ).values('quiz').annotate(
                best_score=Max('score')
            )

            total_score = 0
            quiz_count = 0
            for attempt in quiz_attempts:
                if attempt['best_score'] is not None:
                    total_score += attempt['best_score']
                    quiz_count += 1

            final_score = total_score / quiz_count if quiz_count > 0 else 100.0

            # Create certificate
            certificate = Certificate.objects.create(
                student=self.student,
                course=self.course,
                enrollment=self,
                final_score=final_score,
                completion_date=self.completed_at or timezone.now(),
                is_verified=True
            )
        except Exception as e:
            import traceback
            traceback.print_exc()

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.title}"

    class Meta:
        unique_together = ['student', 'course']
        ordering = ['-enrolled_at']


class Lesson(models.Model):
    """Bài học"""
    CONTENT_TYPES = [
        ('text', 'Nội dung văn bản'),
        ('video', 'Video'),
        ('file', 'Tài liệu'),
    ]

    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name='lessons')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    content_type = models.CharField(
        max_length=20, choices=CONTENT_TYPES, default='text')

    # Content based on type
    content = models.TextField(blank=True)  # For text content (HTML)
    video_url = models.URLField(blank=True)  # For video content
    file = models.FileField(upload_to=upload_lesson_content,
                            blank=True, null=True)  # For file content

    # Lesson details
    duration = models.IntegerField(default=0)  # Minutes
    order = models.IntegerField(default=0)
    # Can be viewed without enrollment
    is_preview = models.BooleanField(default=False)
    is_required = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    class Meta:
        ordering = ['course', 'order']
        unique_together = ['course', 'order']


class LessonProgress(models.Model):
    """Tiến độ học bài"""
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE)
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)

    # Progress tracking
    is_completed = models.BooleanField(default=False)
    time_spent = models.IntegerField(default=0)  # Minutes
    completion_percentage = models.FloatField(
        default=0.0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.lesson.title}"

    class Meta:
        unique_together = ['student', 'lesson']
        ordering = ['-last_accessed']


class Quiz(models.Model):
    """Bài kiểm tra"""
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name='quizzes')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    # Quiz settings
    duration = models.IntegerField(default=30)  # Minutes
    max_attempts = models.IntegerField(default=3)
    passing_score = models.FloatField(default=70.0, validators=[
                                      MinValueValidator(0), MaxValueValidator(100)])
    order = models.IntegerField(default=0)

    # Quiz options
    shuffle_questions = models.BooleanField(default=True)
    show_results_immediately = models.BooleanField(default=True)
    allow_review = models.BooleanField(default=True)
    is_required = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    @property
    def total_questions(self):
        return self.questions.count()

    class Meta:
        ordering = ['course', 'order']
        unique_together = ['course', 'order']


class QuestionBank(models.Model):
    """Ngân hàng câu hỏi của giáo viên"""
    QUESTION_TYPES = [
        ('multiple_choice', 'Trắc nghiệm'),
        ('true_false', 'Đúng/Sai'),
        ('short_answer', 'Trả lời ngắn'),
        ('essay', 'Tự luận'),
    ]

    DIFFICULTY_LEVELS = [
        ('easy', 'Dễ'),
        ('medium', 'Trung bình'),
        ('hard', 'Khó'),
    ]

    teacher = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='question_bank')
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name='questions', null=True, blank=True)
    grade = models.ForeignKey(
        Grade, on_delete=models.CASCADE, related_name='questions', null=True, blank=True)

    question_type = models.CharField(
        max_length=20, choices=QUESTION_TYPES, default='multiple_choice')
    question_text = models.TextField()
    explanation = models.TextField(blank=True)
    points = models.FloatField(default=1.0, validators=[MinValueValidator(0)])
    difficulty = models.CharField(
        max_length=10, choices=DIFFICULTY_LEVELS, default='medium')

    # Tags for categorization
    tags = models.JSONField(default=list, blank=True)

    # For multiple choice questions
    options = models.JSONField(default=list, blank=True)  # List of options
    # List of correct answer indices
    correct_answers = models.JSONField(default=list, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.teacher.username} - {self.question_text[:50]}..."

    class Meta:
        ordering = ['-created_at']


class Question(models.Model):
    """Câu hỏi trong bài kiểm tra"""
    QUESTION_TYPES = [
        ('multiple_choice', 'Trắc nghiệm'),
        ('true_false', 'Đúng/Sai'),
        ('short_answer', 'Trả lời ngắn'),
        ('essay', 'Tự luận'),
    ]

    quiz = models.ForeignKey(
        Quiz, on_delete=models.CASCADE, related_name='questions')
    question_bank = models.ForeignKey(
        QuestionBank, on_delete=models.CASCADE, null=True, blank=True,
        help_text="Reference to question in question bank")

    question_type = models.CharField(
        max_length=20, choices=QUESTION_TYPES, default='multiple_choice')
    question_text = models.TextField()
    explanation = models.TextField(blank=True)
    points = models.FloatField(default=1.0, validators=[MinValueValidator(0)])
    order = models.IntegerField(default=0)

    # For multiple choice questions
    options = models.JSONField(default=list, blank=True)  # List of options
    # List of correct answer indices
    correct_answers = models.JSONField(default=list, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # If linked to question bank, copy data from there
        if self.question_bank:
            self.question_type = self.question_bank.question_type
            self.question_text = self.question_bank.question_text
            self.explanation = self.question_bank.explanation
            self.points = self.question_bank.points
            self.options = self.question_bank.options
            self.correct_answers = self.question_bank.correct_answers
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.quiz.title} - Q{self.order}"

    class Meta:
        ordering = ['quiz', 'order']
        unique_together = ['quiz', 'order']


class QuizAttempt(models.Model):
    """Lần làm bài kiểm tra"""
    STATUS_CHOICES = [
        ('in_progress', 'Đang làm'),
        ('submitted', 'Đã nộp'),
        ('graded', 'Đã chấm'),
        ('expired', 'Hết giờ'),
    ]

    student = models.ForeignKey(User, on_delete=models.CASCADE)
    quiz = models.ForeignKey(
        Quiz, on_delete=models.CASCADE, related_name='attempts')
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)

    # Attempt details
    attempt_number = models.IntegerField(default=1)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='in_progress')

    # Scoring
    score = models.FloatField(null=True, blank=True, validators=[
                              MinValueValidator(0), MaxValueValidator(100)])
    total_points = models.FloatField(default=0.0)
    earned_points = models.FloatField(default=0.0)

    # Timing
    time_limit = models.IntegerField()  # Minutes (copied from quiz at attempt time)
    time_spent = models.IntegerField(default=0)  # Minutes

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField()

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.quiz.title} (Attempt {self.attempt_number})"

    @property
    def is_passed(self):
        return self.score and self.score >= self.quiz.passing_score

    @property
    def time_remaining(self):
        if self.status == 'in_progress':
            return max(0, (self.expires_at - timezone.now()).total_seconds() / 60)
        return 0

    class Meta:
        ordering = ['-started_at']
        unique_together = ['student', 'quiz', 'attempt_number']


class QuizAnswer(models.Model):
    """Câu trả lời của học sinh"""
    attempt = models.ForeignKey(
        QuizAttempt, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey(Question, on_delete=models.CASCADE)

    # Answer data
    answer_data = models.JSONField(default=dict)  # Flexible answer storage
    is_correct = models.BooleanField(
        null=True, blank=True)  # Null for ungraded
    points_earned = models.FloatField(default=0.0)

    # Timestamps
    answered_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.attempt} - Q{self.question.order}"

    class Meta:
        unique_together = ['attempt', 'question']


class Assignment(models.Model):
    """Bài tập TurboWarp"""
    ASSIGNMENT_TYPES = [
        ('turbowarp', 'TurboWarp Project'),
        ('scratch', 'Scratch Project'),
        ('coding', 'Coding Assignment'),
    ]

    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name='assignments')
    title = models.CharField(max_length=255)
    description = models.TextField()
    assignment_type = models.CharField(
        max_length=20, choices=ASSIGNMENT_TYPES, default='turbowarp')

    # Assignment details
    required_features = models.JSONField(default=list, blank=True)
    grading_criteria = models.JSONField(default=list, blank=True)
    starter_project_url = models.URLField(blank=True)
    reference_files = models.FileField(
        upload_to=upload_assignment_file, blank=True, null=True)

    # Assignment settings
    max_score = models.FloatField(default=100.0)
    due_date = models.DateTimeField(null=True, blank=True)
    allow_late_submission = models.BooleanField(default=True)
    max_attempts = models.IntegerField(default=5)
    order = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    class Meta:
        ordering = ['course', 'order']
        unique_together = ['course', 'order']


class AssignmentSubmission(models.Model):
    """Bài nộp của học sinh"""
    STATUS_CHOICES = [
        ('draft', 'Bản nháp'),
        ('submitted', 'Đã nộp'),
        ('graded', 'Đã chấm'),
        ('returned', 'Trả lại'),
    ]

    student = models.ForeignKey(User, on_delete=models.CASCADE)
    assignment = models.ForeignKey(
        Assignment, on_delete=models.CASCADE, related_name='submissions')
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)

    # Submission details
    project_url = models.URLField()  # TurboWarp project URL
    description = models.TextField(blank=True)
    submission_files = models.FileField(
        upload_to=upload_submission_file, blank=True, null=True)

    # Grading
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='draft')
    score = models.FloatField(null=True, blank=True, validators=[
                              MinValueValidator(0), MaxValueValidator(100)])
    feedback = models.TextField(blank=True)
    graded_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name='graded_submissions')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    graded_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.assignment.title}"

    @property
    def is_late(self):
        if self.assignment.due_date and self.submitted_at:
            return self.submitted_at > self.assignment.due_date
        return False

    class Meta:
        ordering = ['-submitted_at']
        unique_together = ['student', 'assignment']


class CourseReview(models.Model):
    """Đánh giá khóa học"""
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name='reviews')
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)

    # Review details
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)])
    title = models.CharField(max_length=255, blank=True)
    comment = models.TextField(blank=True)

    # Review metadata
    is_verified = models.BooleanField(default=False)  # Verified purchase
    is_featured = models.BooleanField(default=False)
    helpful_count = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.title} ({self.rating}★)"

    class Meta:
        unique_together = ['student', 'course']
        ordering = ['-created_at']


class Payment(models.Model):
    """Thanh toán khóa học"""
    PAYMENT_METHODS = [
        ('credit_card', 'Thẻ tín dụng'),
        ('debit_card', 'Thẻ ghi nợ'),
        ('e_wallet', 'Ví điện tử'),
        ('bank_transfer', 'Chuyển khoản'),
        ('cash', 'Tiền mặt'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Chờ xử lý'),
        ('processing', 'Đang xử lý'),
        ('completed', 'Hoàn thành'),
        ('failed', 'Thất bại'),
        ('cancelled', 'Đã hủy'),
        ('refunded', 'Đã hoàn tiền'),
    ]

    # Payment identification
    payment_id = models.UUIDField(
        default=uuid.uuid4, unique=True, editable=False)

    # Payment details
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    enrollment = models.ForeignKey(
        Enrollment, on_delete=models.CASCADE, null=True, blank=True)

    # Amount details
    amount = models.DecimalField(max_digits=10, decimal_places=0)  # VND
    original_amount = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)
    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)

    # Payment method and status
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')

    # External payment data
    external_transaction_id = models.CharField(max_length=255, blank=True)
    # MoMo, ZaloPay, etc.
    payment_gateway = models.CharField(max_length=50, blank=True)
    gateway_response = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.title} - {self.amount} VND"

    @property
    def discount_percentage(self):
        if self.original_amount > 0:
            return int((self.discount_amount / self.original_amount) * 100)
        return 0

    class Meta:
        ordering = ['-created_at']


class Certificate(models.Model):
    """Chứng chỉ hoàn thành khóa học"""
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    enrollment = models.ForeignKey(Enrollment, on_delete=models.CASCADE)

    # Certificate details
    certificate_id = models.UUIDField(
        default=uuid.uuid4, unique=True, editable=False)
    certificate_number = models.CharField(max_length=50, unique=True)

    # Achievement details
    final_score = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(100)])
    completion_date = models.DateTimeField()

    # Certificate metadata
    is_verified = models.BooleanField(default=True)
    verification_url = models.URLField(blank=True)
    certificate_file = models.FileField(
        upload_to='certificates/', blank=True, null=True)

    # Timestamps
    issued_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.title} Certificate"

    def save(self, *args, **kwargs):
        if not self.certificate_number:
            # Generate certificate number: COURSE_YEAR_SEQUENCE
            year = timezone.now().year
            # course_code = self.course.subject.name[:3].upper()
            sequence = Certificate.objects.filter(
                course=self.course,
                issued_at__year=year
            ).count() + 1
            self.certificate_number = f"BeE-{year}-{sequence:04d}"
        super().save(*args, **kwargs)

    class Meta:
        unique_together = ['student', 'course']


# Signals to automatically update enrollment progress
@receiver(post_save, sender=LessonProgress)
def update_enrollment_progress(sender, instance, created, **kwargs):
    """Update enrollment progress when lesson progress is saved"""
    if instance.enrollment:
        try:
            progress_percentage, completed_lessons = instance.enrollment.update_progress()
        except Exception as e:
            import traceback
            traceback.print_exc()
