import axios from "axios";

// authService để xử lý đăng xuất
import authService from "./authService";

// Lấy base URL từ biến môi trường
const BACKEND_URL = import.meta.env.VITE_API_URL || "http://localhost:4000";

// Tạo instance của axios với các cấu hình cơ bản
const axiosInstance = axios.create({
    baseURL: BACKEND_URL,
    headers: {
        "Content-Type": "application/json",
    },
    timeout: 60000, // 60 giây timeout, phù hợp cho mạng chậm
});

// Interceptor cho request: Thêm token vào header và kiểm tra kết nối
axiosInstance.interceptors.request.use(
    (config) => {
        // Kiểm tra kết nối mạng (chỉ áp dụng trên trình duyệt)
        if (typeof window !== "undefined" && !navigator.onLine) {
            throw new Error("No internet connection");
        }

        // Lấy access token từ localStorage hoặc sessionStorage
        const token = localStorage.getItem("access_token") || sessionStorage.getItem("access_token");
        if (token) {
            config.headers["Authorization"] = `Bearer ${token}`;
        }

        // Log request để debug (tùy chọn)
        if (import.meta.env.DEV) {
            console.log(`[Request] ${config.method.toUpperCase()} ${config.url}`, config.data);
        }

        return config;
    },
    (error) => {
        console.error("[Request Error]", error.message);
        return Promise.reject(error);
    }
);

// Interceptor cho response: Xử lý lỗi, refresh token, retry
axiosInstance.interceptors.response.use(
    (response) => {
        // Log response để debug (tùy chọn)
        if (import.meta.env.DEV) {
            console.log(`[Response] ${response.config.method.toUpperCase()} ${response.config.url}`, response.data);
        }
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Xử lý lỗi timeout hoặc mạng, thử lại 1 lần
        if ((error.code === "ECONNABORTED" || !error.response) && !originalRequest?._retry) {
            originalRequest._retry = true;
            console.warn("[Retry] Retrying request due to network issue...");
            await new Promise((resolve) => setTimeout(resolve, 1000)); // Delay 1 giây trước khi thử lại
            return axiosInstance(originalRequest);
        }

        // Xử lý lỗi 401 (token hết hạn) và refresh token
        if (error?.response?.status === 401 && !originalRequest?._retry) {
            originalRequest._retry = true;
            const refreshToken = localStorage.getItem("refresh_token") || sessionStorage.getItem("refresh_token");

            if (refreshToken) {
                try {
                    const response = await axios.post(
                        `${BACKEND_URL}/api/account/token/refresh/`,
                        { refresh: refreshToken },
                        { timeout: 30000 } // Timeout riêng cho refresh token
                    );

                    const newAccessToken = response.data.access;
                    localStorage.setItem("access_token", newAccessToken);
                    axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${newAccessToken}`;

                    // Thử lại yêu cầu ban đầu với token mới
                    originalRequest.headers["Authorization"] = `Bearer ${newAccessToken}`;
                    return axiosInstance(originalRequest);
                } catch (refreshError) {
                    console.error("[Refresh Token Failed]", refreshError.message);
                    authService.logout(); // Đăng xuất nếu refresh token thất bại
                    return Promise.reject(new Error("Session expired, please log in again"));
                }
            } else {
                authService.logout();
                return Promise.reject(error);
            }
        }

        // Xử lý các lỗi khác
        if (!error.response) {
            console.error("[Network Error]", {
                message: error.message,
                code: error.code,
                url: originalRequest?.url,
            });
            return Promise.reject(new Error(`Network Error: ${error.message}`));
        }

        // Lỗi từ server (4xx, 5xx)
        console.error(`[API Error] ${error.response.status}`, error.response.data);
        return Promise.reject(error);
    }
);

export default axiosInstance;
